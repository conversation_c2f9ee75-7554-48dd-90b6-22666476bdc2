# Playwright Automation Script Generator

A command-line tool that generates accurate Playwright automation scripts using AI vision analysis and DOM parsing.

## Features

- Captures screenshots and DOM content of target websites
- Analyzes web pages using OpenAI's vision models
- Generates complete, ready-to-use Playwright scripts
- Supports multiple browsers (Chromium, Firefox, WebKit)
- Configurable script generation with best practices

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd playwright_generator
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Install Playwright browsers:
   ```
   playwright install
   ```

4. Set up your API credentials (for Azure OpenAI):
   ```
   # On Windows
   set OPENAI_API_KEY=your_azure_api_key_here
   set AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com
   
   # On Unix/Linux/Mac
   export OPENAI_API_KEY=your_azure_api_key_here
   export AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com
   ```

## Usage

Basic usage:

```
python main.py --url https://example.com --action "login with username 'admin' and password '123456'"
```

### Available Options

- `--url`, `-u`: Website URL to analyze (required)
- `--action`, `-a`: Description of the action to automate (required)
- `--browser`, `-b`: Browser to use (chromium, firefox, or webkit)
- `--output`, `-o`: Path to save the generated script
- `--config`, `-c`: Path to custom configuration file
- `--verbose`, `-v`: Enable verbose output

### Example

```
python main.py --url https://demo.playwright.dev/todomvc --action "add three todo items and mark the second one as completed" --browser chromium --output ./todo_script.py
```

## Configuration

Default configuration is stored in `config/config.yaml`. You can modify this file or provide your own configuration file using the `--config` option.

Key configuration options:

- OpenAI API settings (API key, model, max tokens, etc.)
- Azure OpenAI settings (endpoint, API version, deployment names)
- Playwright settings (timeout, viewport size, etc.)
- Script generation options (comments, assertions, etc.)
- Output settings (indentation, overwrite behavior, etc.)
- Validation system settings (retries, timeouts, success criteria, etc.)

### Azure OpenAI Configuration

This tool uses Azure OpenAI by default. You need to configure these important settings:

1. Set `use_azure: true` in the config file (already default)
2. Provide your Azure OpenAI endpoint URL via environment variable or config
3. Configure deployment names in the config file:
   - `azure_deployment_name_vision`: Your vision model deployment name
   - `azure_deployment_name_completion`: Your completion model deployment name

Example configuration:

```yaml
openai:
  api_key: "your_azure_api_key" # Better to set via environment variable
  use_azure: true
  azure_endpoint: "https://your-resource.openai.azure.com" # Better to set via environment variable
  azure_api_version: "2023-05-15"
  azure_deployment_name_vision: "gpt-4-vision" # Your actual deployment name
  azure_deployment_name_completion: "gpt-4" # Your actual deployment name
```

To use standard OpenAI instead of Azure, set `use_azure: false` in the config file.

## How It Works

1. The tool navigates to the specified URL using Playwright
2. It captures a screenshot and extracts the DOM content
3. The screenshot is sent to OpenAI's vision model for analysis
4. The DOM content and vision analysis are combined for context
5. An LLM generates an accurate Playwright script based on the context
6. The script is saved to the specified output path

## Requirements

- Python 3.9+
- Playwright
- OpenAI API key
- Internet connection

## License

[MIT License](LICENSE)
