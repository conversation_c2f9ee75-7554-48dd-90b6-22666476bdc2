"""
Script generation module for Playwright automation
"""

import os
import httpx
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from loguru import logger
from openai import OpenAI, AzureOpenAI

from .dom_processor import clean_and_analyze_dom, validate_selectors_with_playwright
from .action_decomposer import ActionDecomposer
from .multi_step_generator import MultiStepScriptGenerator
from .enhanced_script_gen import generate_robust_script


def format_dom_analysis(dom_analysis):
    """Format DOM analysis for inclusion in prompts"""
    if not dom_analysis:
        return "No DOM analysis available."
    
    formatted = []
    
    # Add validated selectors if available
    if dom_analysis.get('validated_selectors'):
        formatted.append("## VALIDATED SELECTORS (TESTED AND WORKING):")
        for selector in dom_analysis['validated_selectors'][:10]:
            formatted.append(f"✅ {selector['selector']} - {selector['element_count']} elements found")
        formatted.append("")
    
    # Add selector suggestions
    if dom_analysis.get('selector_suggestions'):
        formatted.append("## SELECTOR SUGGESTIONS:")
        for suggestion in dom_analysis['selector_suggestions'][:15]:
            formatted.append(f"- {suggestion['selector']} ({suggestion['element_count']} elements)")
        formatted.append("")
    
    # Add interactive elements
    if dom_analysis.get('interactive_elements'):
        formatted.append("## INTERACTIVE ELEMENTS:")
        for element in dom_analysis['interactive_elements'][:10]:
            formatted.append(f"- {element['tag']} with {element.get('attributes', {})}")
        formatted.append("")
    
    # Add form elements
    if dom_analysis.get('form_elements'):
        formatted.append("## FORM ELEMENTS:")
        for form in dom_analysis['form_elements'][:10]:
            formatted.append(f"- {form['tag']}: {form.get('attributes', {})}")
        formatted.append("")
    
    return "\n".join(formatted)


def post_process_script(script_content):
    """Clean and post-process the generated script"""
    if not script_content:
        return script_content
    
    # Remove markdown code blocks
    script_content = script_content.strip()
    
    if script_content.startswith('```python'):
        script_content = script_content[9:]
    elif script_content.startswith('```'):
        script_content = script_content[3:]
    
    if script_content.endswith('```'):
        script_content = script_content[:-3]
    
    return script_content.strip()


def generate_script(dom_content, vision_analysis, action_text, url, page_title, config):
    """
    Main script generation function that routes to appropriate generator
    
    Args:
        dom_content: HTML DOM content
        vision_analysis: Analysis from Vision API
        action_text: Description of the action to automate
        url: URL of the website
        page_title: Title of the webpage
        config: Configuration dictionary
    
    Returns:
        Tuple of (script_content, dom_analysis)
    """
    logger.info(f"Generating script for action: {action_text}")
    
    # Determine if this is a multi-step action
    decomposer = ActionDecomposer()
    is_multi_step = decomposer.is_multi_step_action(action_text)
    
    if is_multi_step:
        logger.info("Detected multi-step action, using multi-step generator")
        return generate_multi_step_script(dom_content, vision_analysis, action_text, url, page_title, config)
    else:
        logger.info("Detected single-step action, using single-step generator")
        return generate_single_step_script(dom_content, vision_analysis, action_text, url, page_title, config)


def generate_multi_step_script(dom_content, vision_analysis, action_text, url, page_title, config):
    """Generate a multi-step Playwright script"""
    try:
        logger.info("Generating multi-step script")
        
        # Initialize the multi-step generator
        generator = MultiStepScriptGenerator(config)
        
        # Generate the script
        script_content, dom_analysis = generator.generate_script(
            dom_content=dom_content,
            vision_analysis=vision_analysis,
            action_text=action_text,
            url=url,
            page_title=page_title
        )
        
        if script_content:
            logger.info("Successfully generated multi-step script")
            return script_content, dom_analysis
        else:
            logger.error("Failed to generate multi-step script")
            return None, None
            
    except Exception as e:
        logger.error(f"Error in multi-step script generation: {str(e)}")
        return None, None


def generate_single_step_script(dom_content, vision_analysis, action_text, url, page_title, config):
    """Generate a single-step Playwright script using enhanced robust generation"""
    logger.info("Using enhanced robust script generation for single-step action")
    return generate_robust_script(dom_content, vision_analysis, action_text, url, page_title, config)


def save_script_to_file(script_content, filename="output_script.py"):
    """Save the generated script to a file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(script_content)
        logger.info(f"Script saved to {filename}")
        return True
    except Exception as e:
        logger.error(f"Error saving script: {str(e)}")
        return False


def save_script(script, output_path, config):
    """
    Save generated script to file with advanced options
    
    Args:
        script (str): Generated script content
        output_path (str): Path to save the script
        config (dict): Configuration dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    output_config = config.get('output', {})
    force_overwrite = output_config.get('force_overwrite', False)
    
    # Check if file exists and we're not forcing overwrite
    if os.path.exists(output_path) and not force_overwrite:
        logger.warning(f"Output file already exists: {output_path}")
        # Append a number to the filename
        base, ext = os.path.splitext(output_path)
        counter = 1
        while os.path.exists(f"{base}_{counter}{ext}"):
            counter += 1
        output_path = f"{base}_{counter}{ext}"
        logger.info(f"Using alternative output path: {output_path}")
    
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(script)
        
        logger.info(f"Script saved to: {output_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving script: {str(e)}")
        return False
