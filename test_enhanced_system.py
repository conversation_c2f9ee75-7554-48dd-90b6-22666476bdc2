#!/usr/bin/env python3
"""
Test script for the enhanced Playwright script generation system
"""

import sys
import os
from loguru import logger

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.script_gen import generate_script
from src.config import load_config


def test_enhanced_system():
    """Test the enhanced script generation system"""
    
    # Load configuration
    config = load_config('config/config.yaml')
    
    # Test data
    test_url = "https://www.google.com"
    test_action = "search for 'playwright automation'"
    test_page_title = "Google"
    test_dom_content = """
    <html>
        <body>
            <form>
                <input name="q" type="text" placeholder="Search">
                <button type="submit">Google Search</button>
            </form>
        </body>
    </html>
    """
    test_vision_analysis = "The page shows a Google search interface with a search input field and search button."
    
    logger.info("Testing enhanced script generation system...")
    
    try:
        # Generate script using the enhanced system
        script_content, dom_analysis = generate_script(
            dom_content=test_dom_content,
            vision_analysis=test_vision_analysis,
            action_text=test_action,
            url=test_url,
            page_title=test_page_title,
            config=config
        )
        
        if script_content:
            logger.success("✅ Enhanced script generation successful!")
            logger.info(f"Generated script length: {len(script_content)} characters")
            
            # Check if script contains key robust features
            robust_features = [
                "create_robust_browser_context",
                "wait_for_element_with_fallbacks", 
                "human_like_delay",
                "check_for_captcha_or_blocking",
                "TimeoutError"
            ]
            
            found_features = []
            for feature in robust_features:
                if feature in script_content:
                    found_features.append(feature)
            
            logger.info(f"✅ Found {len(found_features)}/{len(robust_features)} robust features:")
            for feature in found_features:
                logger.info(f"  - {feature}")
            
            # Save test script
            with open("test_generated_script.py", "w") as f:
                f.write(script_content)
            logger.info("✅ Test script saved to test_generated_script.py")
            
            return True
        else:
            logger.error("❌ Script generation failed - no content returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error during script generation: {str(e)}")
        return False


if __name__ == "__main__":
    logger.info("🚀 Starting enhanced system test...")
    success = test_enhanced_system()
    
    if success:
        logger.success("🎉 Enhanced system test completed successfully!")
        sys.exit(0)
    else:
        logger.error("💥 Enhanced system test failed!")
        sys.exit(1)
